import { useAuth } from "@/context/AuthContext";
import {
  appleAuth,
  appleAuthAndroid,
  AppleButton,
} from "@invertase/react-native-apple-authentication";
import { router } from "expo-router";
import React, { useEffect } from "react";
import { Alert, Platform } from "react-native";
import "react-native-get-random-values";
import { v4 as uuid } from "uuid";

interface AppleButtonProps {
  signup?: boolean;
}

export function AppleSignInButton({ signup = false }: AppleButtonProps) {
  const { signIn, signOut } = useAuth();

  // Set up credential revocation listener for iOS
  useEffect(() => {
    if (Platform.OS === "ios") {
      // onCredentialRevoked returns a function that will remove the event listener
      return appleAuth.onCredentialRevoked(async () => {
        console.warn("Apple credentials have been revoked - signing out user");
        await signOut();
        router.replace("/login");
      });
    }
  }, [signOut]); // Include signOut in dependencies

  const handleAppleSignIn = async () => {
    try {
      if (Platform.OS === "ios") {
        // iOS Implementation
        const appleAuthRequestResponse = await appleAuth.performRequest({
          requestedOperation: appleAuth.Operation.LOGIN,
          // Note: FULL_NAME should be first according to the docs
          requestedScopes: [appleAuth.Scope.FULL_NAME, appleAuth.Scope.EMAIL],
        });

        console.log("Apple Sign In Response:", appleAuthRequestResponse);

        // Get current authentication state for user
        // Note: This method must be tested on a real device. On the iOS simulator it always throws an error.
        const credentialState = await appleAuth.getCredentialStateForUser(
          appleAuthRequestResponse.user
        );

        console.log("Apple Credential State:", credentialState);

        if (credentialState === appleAuth.State.AUTHORIZED) {
          // User is authenticated - send to your backend API
          await sendToBackend(appleAuthRequestResponse, "ios");
        } else {
          Alert.alert(
            "Authentication Failed",
            "Apple authentication was not successful. Please try again."
          );
        }
      } else if (Platform.OS === "android") {
        // Android Implementation
        if (!appleAuthAndroid.isSupported) {
          Alert.alert(
            "Not Supported",
            "Apple Sign In is not supported on this Android device"
          );
          return;
        }

        // Generate secure, random values for state and nonce
        const rawNonce = uuid();
        const state = uuid();

        // Configure the request
        appleAuthAndroid.configure({
          // TODO: Replace with your actual Service ID from Apple Developer Console
          clientId: "com.example.client-android", // Replace with your actual Service ID

          // TODO: Replace with your actual redirect URI from Apple Developer Console
          redirectUri: "https://example.com/auth/callback", // Replace with your actual redirect URI

          // The type of response requested
          responseType: appleAuthAndroid.ResponseType.ALL,

          // The amount of user information requested from Apple
          scope: appleAuthAndroid.Scope.ALL,

          // Random nonce value that will be SHA256 hashed before sending to Apple
          nonce: rawNonce,

          // Unique state value used to prevent CSRF attacks
          state,
        });

        console.log("Apple Android configuration set, opening sign-in...");

        // Open the browser window for user sign in
        const response = await appleAuthAndroid.signIn();

        console.log("Apple Android Sign In Response:", response);

        // Send the authorization code to your backend for verification
        await sendToBackend(response, "android");
      }
    } catch (error: any) {
      console.error("Apple Sign In Error:", error);

      if (error.code === appleAuth.Error.CANCELED) {
        console.log("User canceled Apple Sign In");
      } else if (error.code === "ERR_REQUEST_CANCELED") {
        console.log("User canceled Apple Sign In");
      } else {
        Alert.alert(
          "Sign In Failed",
          "Apple Sign In failed. Please try again."
        );
      }
    }
  };

  const sendToBackend = async (
    authResponse: any,
    platform: "ios" | "android"
  ) => {
    try {
      const response = await fetch(
        process.env.EXPO_PUBLIC_API_DOMAIN + "/auth/signinup",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json; charset=utf-8",
          },
          body: JSON.stringify({
            thirdPartyId: "apple",
            platform,
            redirectURIInfo: {
              redirectURIOnProviderDashboard:
                platform === "ios"
                  ? "scribeapp://auth/callback/apple"
                  : "https://example.com/auth/callback", // TODO: Replace with your actual redirect URI
              redirectURIQueryParams:
                platform === "ios"
                  ? {
                      code: authResponse.authorizationCode,
                      id_token: authResponse.identityToken,
                      user: authResponse.user,
                    }
                  : {
                      code: authResponse.authorization?.code,
                      id_token: authResponse.authorization?.id_token,
                      state: authResponse.authorization?.state,
                    },
            },
          }),
        }
      );

      const data = await response.json();

      switch (data.status) {
        case "OK":
          console.log("Apple signin successfully");
          await signIn(data.accessToken, data.expiresInSeconds);
          router.replace("/(app)/home");
          break;
        case "NO_EMAIL_GIVEN_BY_PROVIDER":
          console.error("No email provided by Apple");
          Alert.alert(
            "Sign In Failed",
            "No email provided by Apple. Please try another sign-in method."
          );
          break;
        case "SIGN_IN_UP_NOT_ALLOWED":
          console.error("Sign in/up not allowed", data.reason);
          Alert.alert(
            "Sign In Failed",
            "Sign in/up not allowed. Please contact support."
          );
          break;
        case "GENERAL_ERROR":
          console.error("General error");
          Alert.alert("Sign In Failed", "An error occurred. Please try again.");
          break;
        default:
          console.error("Unexpected error:", data);
          Alert.alert(
            "Sign In Failed",
            "An unexpected error occurred. Please try again."
          );
      }
    } catch (error) {
      console.error("Backend request failed:", error);
      Alert.alert(
        "Sign In Failed",
        "Network error. Please check your connection and try again."
      );
    }
  };

  // Show button only if Apple Sign In is supported
  if (Platform.OS === "android" && !appleAuthAndroid.isSupported) {
    return null;
  }

  return (
    <AppleButton
      buttonStyle={AppleButton.Style.BLACK}
      buttonType={signup ? AppleButton.Type.SIGN_UP : AppleButton.Type.SIGN_IN}
      style={{
        width: "100%",
        height: 44,
        marginTop: 8,
      }}
      onPress={handleAppleSignIn}
    />
  );
}
